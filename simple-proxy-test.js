const Imap = require('imap');
const { SocksProxyAgent } = require('socks-proxy-agent');
const { HttpProxyAgent } = require('http-proxy-agent');
const { HttpsProxyAgent } = require('https-proxy-agent');
const fs = require('fs');
const net = require('net');

// Load a random proxy from proxy.txt
function getRandomProxy() {
    try {
        const proxyData = fs.readFileSync('proxy.txt', 'utf8');
        const proxies = proxyData.split('\n').filter(line => line.trim());
        
        if (proxies.length === 0) {
            console.log('No proxies found in proxy.txt');
            return null;
        }
        
        const randomProxy = proxies[Math.floor(Math.random() * proxies.length)];
        const [host, port] = randomProxy.split(':');
        
        return {
            host: host,
            port: parseInt(port),
            url: `http://${host}:${port}`
        };
    } catch (error) {
        console.log('Error loading proxies:', error.message);
        return null;
    }
}

// Create a socket through proxy using HTTP CONNECT
function createProxySocket(proxy, targetHost, targetPort) {
    return new Promise((resolve, reject) => {
        const socket = new net.Socket();
        let connected = false;
        
        const timeout = setTimeout(() => {
            if (!connected) {
                socket.destroy();
                reject(new Error('Proxy connection timeout'));
            }
        }, 10000);
        
        socket.connect(proxy.port, proxy.host, () => {
            // Send HTTP CONNECT request
            const connectRequest = `CONNECT ${targetHost}:${targetPort} HTTP/1.1\r\nHost: ${targetHost}:${targetPort}\r\nConnection: close\r\n\r\n`;
            socket.write(connectRequest);
        });
        
        socket.once('data', (data) => {
            const response = data.toString();
            if (response.includes('200 Connection established') || response.includes('200 OK')) {
                connected = true;
                clearTimeout(timeout);
                resolve(socket);
            } else {
                clearTimeout(timeout);
                socket.destroy();
                reject(new Error(`Proxy CONNECT failed: ${response.split('\r\n')[0]}`));
            }
        });
        
        socket.on('error', (err) => {
            clearTimeout(timeout);
            reject(err);
        });
    });
}

// Test GMX credential with proxy
async function testGmxCredentialWithProxy(email, password, useProxy = true) {
    return new Promise(async (resolve) => {
        console.log(`Testing: ${email} ${useProxy ? '(with proxy)' : '(direct)'}`);
        
        let config = {
            user: email,
            password: password,
            host: 'imap.gmx.net',
            port: 143,
            tls: false,
            autotls: 'required',
            tlsOptions: {
                rejectUnauthorized: false
            },
            connTimeout: 15000,
            authTimeout: 10000
        };
        
        let proxyInfo = null;
        
        if (useProxy) {
            const proxy = getRandomProxy();
            if (!proxy) {
                console.log(`❌ ${email} - No proxy available`);
                return resolve({ email, valid: false, error: 'No proxy available' });
            }
            
            proxyInfo = `${proxy.host}:${proxy.port}`;
            console.log(`Using proxy: ${proxyInfo}`);
            
            try {
                // Create socket through proxy
                const proxySocket = await createProxySocket(proxy, 'imap.gmx.net', 143);
                config.socket = proxySocket;
            } catch (error) {
                console.log(`❌ ${email} - Proxy error: ${error.message}`);
                return resolve({ email, valid: false, error: `Proxy error: ${error.message}` });
            }
        }

        const imap = new Imap(config);
        let resolved = false;

        // Timeout handler
        const timeoutId = setTimeout(() => {
            if (!resolved) {
                resolved = true;
                imap.destroy();
                console.log(`❌ ${email} - Timeout ${proxyInfo ? `[${proxyInfo}]` : ''}`);
                resolve({ email, valid: false, error: 'Timeout' });
            }
        }, 20000);

        // Success handler
        imap.once('ready', () => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                imap.end();
                console.log(`✅ ${email} - Valid ${proxyInfo ? `[${proxyInfo}]` : ''}`);
                resolve({ email, valid: true });
            }
        });

        // Error handler
        imap.once('error', (err) => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                
                const errorMsg = err.message || err.toString();
                console.log(`❌ ${email} - Error: ${errorMsg} ${proxyInfo ? `[${proxyInfo}]` : ''}`);
                
                // Check if it's an authentication error (invalid credentials)
                const isAuthError = errorMsg.toLowerCase().includes('login') || 
                                  errorMsg.toLowerCase().includes('authentication') ||
                                  errorMsg.toLowerCase().includes('invalid');
                
                resolve({ 
                    email, 
                    valid: false, 
                    error: errorMsg,
                    isAuthError: isAuthError
                });
            }
        });

        // Start connection
        try {
            imap.connect();
        } catch (error) {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                console.log(`❌ ${email} - Connection error: ${error.message} ${proxyInfo ? `[${proxyInfo}]` : ''}`);
                resolve({ email, valid: false, error: error.message });
            }
        }
    });
}

// Test function
async function runTest() {
    console.log('=== Simple GMX Validator Test with Proxy ===\n');
    
    // Test credentials
    const testCredentials = [
        { email: '<EMAIL>', password: 'wrongpassword' },
        { email: '<EMAIL>', password: 'F3hmarnx!1337' }
    ];
    
    for (const cred of testCredentials) {
        // Try with proxy first
        const result = await testGmxCredentialWithProxy(cred.email, cred.password, true);
        
        if (result.valid) {
            console.log(`✅ VALID: ${result.email}`);
        } else if (result.isAuthError) {
            console.log(`❌ INVALID: ${result.email} - ${result.error}`);
        } else {
            console.log(`⚠️  ERROR: ${result.email} - ${result.error}`);
        }
        console.log(''); // Empty line for readability
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('Test completed!');
    process.exit(0);
}

// Run the test
runTest().catch(console.error);
