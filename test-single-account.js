const Imap = require('imap');

console.log('Testing single GMX account with STARTTLS configuration...');

const config = {
    user: '<EMAIL>',
    password: 'F3hmarnx!1337',
    host: 'imap.gmx.net',
    port: 143,
    tls: false,
    autotls: 'required', // Force STARTTLS upgrade
    tlsOptions: {
        rejectUnauthorized: false
    },
    connTimeout: 30000,
    authTimeout: 20000,
    debug: (info) => {
        console.log(`[RAW IMAP] ${info}`);
    }
};

console.log('Configuration:', {
    ...config,
    password: '***HIDDEN***'
});

const imap = new Imap(config);

imap.once('ready', function() {
    console.log('✅ SUCCESS: IMAP connection established and authenticated!');
    console.log('✅ <NAME_EMAIL> is VALID');
    imap.end();
});

imap.once('error', function(err) {
    console.log('❌ IMAP Error:', err.message);
    console.log('❌ Error details:', err);
});

imap.once('end', function() {
    console.log('🔌 Connection ended');
    process.exit(0);
});

console.log('Connecting to GMX IMAP server...');
imap.connect();
