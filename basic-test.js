const Imap = require('imap');

console.log('=== Basic GMX Connection Test ===\n');

// Test 1: Basic connectivity test
console.log('Test 1: Basic IMAP connection test...');

const config = {
    user: '<EMAIL>',
    password: 'wrongpassword',
    host: 'imap.gmx.net',
    port: 143,
    tls: false,
    autotls: 'required',
    tlsOptions: {
        rejectUnauthorized: false
    },
    connTimeout: 5000,
    authTimeout: 5000,
    debug: console.log // Enable debug output
};

const imap = new Imap(config);

imap.once('ready', () => {
    console.log('✅ IMAP connection successful!');
    console.log('✅ Server is reachable and responding');
    imap.end();
});

imap.once('error', (err) => {
    console.log('Connection result:', err.message);
    
    if (err.message.includes('authentication') || err.message.includes('login')) {
        console.log('✅ Server is reachable (got authentication error as expected)');
        console.log('✅ Network connectivity is working');
    } else if (err.message.includes('timeout') || err.message.includes('connect')) {
        console.log('❌ Network/connectivity issue');
        console.log('❌ IMAP port might be blocked');
    } else {
        console.log('⚠️  Unknown error type');
    }
});

imap.once('end', () => {
    console.log('\nConnection ended');
    
    setTimeout(() => {
        console.log('\n=== Summary ===');
        console.log('If you saw "authentication" error: Network is working, just wrong credentials');
        console.log('If you saw "timeout" error: Network/IMAP port is blocked');
        console.log('If you saw "ready": Connection fully successful');
        process.exit(0);
    }, 1000);
});

console.log('Connecting to imap.gmx.net:143...');
imap.connect();
