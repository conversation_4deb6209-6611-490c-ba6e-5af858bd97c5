# NIXNODE GMX Validator

**Professional Email Validation Tool by NIXNODE**

A high-performance Node.js application for validating GMX email credentials using IMAP with concurrent worker threads and proxy support. Developed by NIXNODE for enterprise-grade email validation needs.

## Features

- ✅ **Concurrent Processing**: Uses worker threads for simultaneous validation
- 🔒 **Proxy Support**: HTTP, HTTPS, and SOCKS5 proxy support with rotation
- 📊 **Statistics**: Detailed validation and proxy usage statistics
- 🚀 **High Performance**: Handles thousands of email credentials efficiently
- 📝 **Detailed Logging**: Progress tracking and error reporting
- 🔄 **Proxy Rotation**: Automatic proxy rotation with failure handling

## Installation

1. Clone or download this project
2. Install dependencies:
```bash
npm install
```

## Usage

### Basic Usage
```bash
node index.js
```

### With Custom Options
```bash
node index.js --input myemails.txt --workers 20 --proxy myproxies.txt
```

### Without Proxy (Direct Connection)
```bash
node index.js --no-proxy --workers 5
```

## Command Line Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--input` | `-i` | Input file with email:password format | `emails.txt` |
| `--workers` | `-w` | Number of concurrent workers | `10` |
| `--good` | `-g` | Output file for valid credentials | `good.txt` |
| `--bad` | `-b` | Output file for invalid credentials | `bad.txt` |
| `--proxy` | `-p` | Proxy file | `proxy.txt` |
| `--no-proxy` | | Disable proxy support | `false` |
| `--help` | `-h` | Show help message | |

## File Formats

### Email Input File (`emails.txt`)
Each line should contain an email and password separated by a colon:
```
<EMAIL>:password123
<EMAIL>:mypassword
<EMAIL>:secret456
```

### Proxy File (`proxy.txt`)
Supports multiple proxy formats:
```
# IP:Port
*******:8080
*******:3128

# IP:Port:Username:Password
proxy.example.com:8080:username:password

# HTTP Proxies
http://proxy.example.com:3128
http://user:<EMAIL>:8080

# SOCKS5 Proxies
socks5://proxy.example.com:1080
socks5://user:<EMAIL>:1080
```

## Output Files

### Valid Credentials (`good.txt`)
Contains successfully validated email:password combinations:
```
<EMAIL>:correctpassword
<EMAIL>:workingpass
```

### Invalid Credentials (`bad.txt`)
Contains failed validations with error details:
```
<EMAIL>:wrongpass - Invalid credentials
<EMAIL>:badpass - Connection failed: ECONNREFUSED
```

## Configuration

The application includes advanced configuration options in `config.js`:

- **Worker Settings**: Min/max workers, timeouts
- **Proxy Settings**: Rotation strategies, failure handling
- **SMTP Settings**: Connection timeouts, retry logic
- **Rate Limiting**: Requests per second limits
- **Logging**: Debug levels, progress intervals

## Performance Tips

1. **Worker Count**: Start with 10-20 workers, adjust based on your system and network
2. **Proxy Quality**: Use high-quality, fast proxies for better performance
3. **Rate Limiting**: Don't set too many workers to avoid being blocked
4. **Batch Processing**: Process large lists in smaller batches

## Supported GMX Domains

- gmx.de
- gmx.net
- gmx.com
- gmx.at
- gmx.ch

## Error Handling

The application handles various error scenarios:
- Network timeouts
- Proxy failures with automatic rotation
- SMTP authentication errors
- Connection refused errors
- Invalid email formats

## Statistics

After completion, the application shows:
- Total processed emails
- Valid/invalid counts
- Success rates
- Proxy usage statistics
- Performance metrics

## Example Output

```
=== NIXNODE GMX Validator ===
Professional Email Validation Tool
Copyright © 2024 NIXNODE. All rights reserved.

Loading proxies from proxy.txt...
Loaded 9 proxies
Initializing 10 worker threads...
Worker 1 ready
Worker 2 ready
...
Found 1000 email credentials to validate

Starting validation with 10 concurrent workers with proxy support...

✓ Valid: <EMAIL> [*******:8080] (1/1000)
✗ Invalid: <EMAIL> - Invalid credentials [*******:3128] (2/1000)
...

=== Validation Complete ===
Total processed: 1000
Valid credentials: 45
Invalid credentials: 955
Success rate: 4.50%

=== Proxy Statistics ===
*******:8080: Used 112, Success 5, Failed 107 (4.46% success rate)
*******:3128: Used 108, Success 4, Failed 104 (3.70% success rate)
...

✓ Saved 45 valid credentials to good.txt
✓ Saved 955 invalid credentials to bad.txt

Validation process completed!
```

## Security Notes

- Keep your proxy list secure
- Don't log valid credentials in production
- Use rate limiting to avoid being blocked
- Consider using residential proxies for better success rates

## Troubleshooting

1. **No proxies loaded**: Check proxy.txt format and file permissions
2. **All workers failing**: Check network connectivity and proxy validity
3. **Low success rate**: Verify email format and proxy quality
4. **Connection timeouts**: Increase timeout values in config.js

## License

This project is for educational purposes. Please respect GMX.de's terms of service and use responsibly.
