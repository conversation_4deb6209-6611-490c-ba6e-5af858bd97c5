GMX Validator - Customer Instructions
=====================================

QUICK START:
1. Double-click 'run.bat' to start the program easily
   OR
   Double-click 'germanydazzyproject.exe' directly

SETUP:
1. Place your email:password list in 'emails.txt' (format: email:password, one per line)
2. If you have proxies, place them in 'proxy.txt' (one per line)
3. Make sure all files are in the same folder as the executable

RUNNING:
- Easy way: Double-click 'run.bat'
- Advanced: Run 'germanydazzyproject.exe' from command prompt
- For help: germanydazzyproject.exe --help

RESULTS:
- Valid credentials will be saved in 'good.txt'
- Invalid credentials will be saved in 'bad.txt'

TROUBLESHOOTING:
- If the program doesn't start, make sure you have Windows 10 or newer
- If you get security warnings, click "More info" then "Run anyway"
- Make sure emails.txt is in the correct format (email:password)

SUPPORT:
Contact your provider if you encounter any issues.
