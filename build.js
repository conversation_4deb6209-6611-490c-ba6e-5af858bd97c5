#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building executable for GMX Validator...\n');

// Create dist directory if it doesn't exist
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
    console.log('✅ Created dist directory');
}

try {
    // Build for Windows (most common for customers)
    console.log('🔨 Building Windows executable...');
    execSync('npm run build-win', { stdio: 'inherit' });
    
    // Copy necessary files to dist
    const filesToCopy = ['emails.txt', 'proxy.txt', 'README.md'];
    
    console.log('\n📁 Copying necessary files...');
    filesToCopy.forEach(file => {
        if (fs.existsSync(file)) {
            fs.copyFileSync(file, path.join(distDir, file));
            console.log(`✅ Copied ${file}`);
        }
    });
    
    // Create a simple instruction file for the customer
    const instructions = `GMX Validator - Customer Instructions
=====================================

1. Place your email:password list in 'emails.txt' (one per line)
2. If you have proxies, place them in 'proxy.txt' (one per line)
3. Run the executable: germanydazzyproject.exe
4. Follow the interactive prompts
5. Results will be saved in 'good.txt' and 'bad.txt'

For help, run: germanydazzyproject.exe --help

Note: Make sure all files (emails.txt, proxy.txt) are in the same folder as the executable.
`;
    
    fs.writeFileSync(path.join(distDir, 'INSTRUCTIONS.txt'), instructions);
    console.log('✅ Created INSTRUCTIONS.txt');
    
    console.log('\n🎉 Build completed successfully!');
    console.log(`📦 Executable and files are in the 'dist' folder`);
    console.log(`💡 You can now zip the 'dist' folder and send it to your customer`);
    
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
}
