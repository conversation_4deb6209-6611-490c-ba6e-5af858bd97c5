#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building executable for GMX Validator...\n');

// Create dist directory if it doesn't exist
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
    console.log('✅ Created dist directory');
}

try {
    // Build for Windows (most common for customers)
    console.log('🔨 Building Windows executable...');
    execSync('npm run build-win', { stdio: 'inherit' });
    
    // Copy necessary files to dist
    const filesToCopy = ['emails.txt', 'proxy.txt', 'README.md', 'smtp-worker.js'];

    console.log('\n📁 Copying necessary files...');
    filesToCopy.forEach(file => {
        if (fs.existsSync(file)) {
            fs.copyFileSync(file, path.join(distDir, file));
            console.log(`✅ Copied ${file}`);
        }
    });
    
    // Create a simple instruction file for the customer
    const instructions = `GMX Validator - Customer Instructions
=====================================

QUICK START:
1. Double-click 'run.bat' to start the program easily
   OR
   Double-click 'germanydazzyproject.exe' directly

SETUP:
1. Place your email:password list in 'emails.txt' (format: email:password, one per line)
2. If you have proxies, place them in 'proxy.txt' (one per line)
3. Keep ALL files in the same folder as the executable (including smtp-worker.js)

RUNNING:
- Easy way: Double-click 'run.bat'
- Advanced: Run 'germanydazzyproject.exe' from command prompt
- For help: germanydazzyproject.exe --help

RESULTS:
- Valid credentials will be saved in 'good.txt'
- Invalid credentials will be saved in 'bad.txt'

TROUBLESHOOTING:
- If the program doesn't start, make sure you have Windows 10 or newer
- If you get security warnings, click "More info" then "Run anyway"
- Make sure emails.txt is in the correct format (email:password)
- DO NOT move or delete smtp-worker.js - it's required for the program to work

SUPPORT:
Contact your provider if you encounter any issues.
`;
    
    fs.writeFileSync(path.join(distDir, 'INSTRUCTIONS.txt'), instructions);
    console.log('✅ Created INSTRUCTIONS.txt');
    
    console.log('\n🎉 Build completed successfully!');
    console.log(`📦 Executable and files are in the 'dist' folder`);
    console.log(`💡 You can now zip the 'dist' folder and send it to your customer`);
    
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
}
