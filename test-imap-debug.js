const Imap = require('imap');

console.log('Testing IMAP connection to GMX...');

const config = {
    user: '<EMAIL>',
    password: 'F3hmarnx!1337',
    host: 'imap.gmx.net',
    port: 143,
    tls: false,
    autotls: 'required',
    tlsOptions: {
        rejectUnauthorized: false
    },
    connTimeout: 30000,
    authTimeout: 20000,
    debug: console.log // Enable debug logging
};

const imap = new Imap(config);

imap.once('ready', () => {
    console.log('✅ IMAP connection successful!');
    imap.end();
});

imap.once('error', (err) => {
    console.log('❌ IMAP error:', err.message);
    console.log('Error details:', err);
});

imap.once('end', () => {
    console.log('Connection ended');
    process.exit(0);
});

console.log('Attempting to connect...');
imap.connect();

// Timeout after 60 seconds
setTimeout(() => {
    console.log('⏰ Manual timeout after 60 seconds');
    imap.destroy();
    process.exit(1);
}, 60000);
