#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📦 Creating customer package...\n');

try {
    // Build the executable
    console.log('🔨 Building executable...');
    execSync('npm run build-customer', { stdio: 'inherit' });
    
    // Create a zip file for easy distribution
    const zipName = `gmx-validator-${new Date().toISOString().split('T')[0]}.zip`;
    
    console.log(`\n📁 Creating zip file: ${zipName}`);
    
    // Check if zip command is available (macOS/Linux)
    try {
        execSync(`cd dist && zip -r ../${zipName} .`, { stdio: 'inherit' });
        console.log(`✅ Created ${zipName}`);
    } catch (error) {
        console.log('⚠️  Zip command not available. Please manually zip the dist folder.');
    }
    
    console.log('\n🎉 Customer package ready!');
    console.log('📋 What to send to your customer:');
    console.log(`   - Either the zip file: ${zipName}`);
    console.log('   - Or the entire "dist" folder');
    console.log('\n💡 The customer just needs to:');
    console.log('   1. Extract the files (if zipped)');
    console.log('   2. Put their emails in emails.txt');
    console.log('   3. Double-click run.bat or the .exe file');
    
} catch (error) {
    console.error('❌ Failed to create package:', error.message);
    process.exit(1);
}
