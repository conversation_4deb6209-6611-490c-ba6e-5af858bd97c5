#!/usr/bin/env node

const GmxValidator = require('./gmx-validator');
const { showWelcomeBanner, getInteractiveConfig, showConfigSummary, confirmProceed } = require('./interactive-cli');

// Default configuration
const defaultConfig = {
    maxWorkers: 50,           // Number of concurrent workers (increased for speed)
    inputFile: 'emails.txt',  // Input file with email:password format
    goodFile: 'good.txt',     // Output file for valid credentials
    badFile: 'bad.txt',       // Output file for invalid credentials
    proxyFile: 'proxy.txt',   // Proxy file
    useProxy: true            // Enable proxy support
};

// Parse command line arguments
function parseArgs() {
    const args = process.argv.slice(2);
    const options = { ...defaultConfig };
    
    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--input':
            case '-i':
                options.inputFile = args[++i];
                break;
            case '--workers':
            case '-w':
                options.maxWorkers = parseInt(args[++i]) || 10;
                break;
            case '--good':
            case '-g':
                options.goodFile = args[++i];
                break;
            case '--bad':
            case '-b':
                options.badFile = args[++i];
                break;
            case '--proxy':
            case '-p':
                options.proxyFile = args[++i];
                break;
            case '--no-proxy':
                options.useProxy = false;
                break;
            case '--fast':
                options.maxWorkers = 100;
                console.log('🚀 Fast mode enabled: 100 workers');
                break;
            case '--turbo':
                options.maxWorkers = 200;
                console.log('⚡ Turbo mode enabled: 200 workers');
                break;
            case '--extreme':
                options.maxWorkers = 500;
                console.log('🔥 Extreme mode enabled: 500 workers (use with caution!)');
                break;
            case '--help':
            case '-h':
                showHelp();
                process.exit(0);
            default:
                console.error(`Unknown option: ${args[i]}`);
                showHelp();
                process.exit(1);
        }
    }
    
    return options;
}

function showHelp() {
    console.log(`
NIXNODE GMX Validator - Professional Email Validation Tool
Copyright © 2024 NIXNODE. All rights reserved.

Usage: node index.js [options]

Options:
  -i, --input <file>     Input file with email:password format (default: emails.txt)
  -w, --workers <num>    Number of concurrent workers (default: 50)
  -g, --good <file>      Output file for valid credentials (default: good.txt)
  -b, --bad <file>       Output file for invalid credentials (default: bad.txt)
  -p, --proxy <file>     Proxy file (default: proxy.txt)
  --no-proxy             Disable proxy support (use direct connections)
  --fast                 Fast mode: 100 concurrent workers
  --turbo                Turbo mode: 200 concurrent workers
  --extreme              Extreme mode: 500 concurrent workers (use with caution!)
  -h, --help             Show this help message

Input file format:
  Each line should contain: email:password
  Example:
    <EMAIL>:password123
    <EMAIL>:mypassword
    <EMAIL>:secret456

Proxy file format:
  Each line should contain a proxy in one of these formats:
    ip:port
    ip:port:username:password
    http://ip:port
    ***************************:port
    socks5://ip:port
    socks5://username:password@ip:port

Output:
  - Valid credentials will be saved to the good file
  - Invalid credentials will be saved to the bad file with error details
  - Proxy statistics will be displayed at the end

Examples:
  node index.js --input myemails.txt --workers 20 --good valid.txt --bad invalid.txt
  node index.js --proxy myproxies.txt --workers 15
  node index.js --no-proxy --workers 5
`);
}

// Main execution
async function main() {
    try {
        const args = process.argv.slice(2);

        // Check if running in interactive mode (no arguments provided)
        if (args.length === 0) {
            await runInteractiveMode();
        } else {
            await runCommandLineMode(args);
        }

    } catch (error) {
        console.error('Fatal error:', error.message);
        process.exit(1);
    }
}

// Interactive mode
async function runInteractiveMode() {
    showWelcomeBanner();

    try {
        const config = await getInteractiveConfig();
        showConfigSummary(config);

        const proceed = await confirmProceed();
        if (!proceed) {
            console.log('\n👋 Operation cancelled. Goodbye!');
            process.exit(0);
        }

        console.log('\n🚀 Starting validation...\n');
        const validator = new GmxValidator(config);
        validatorInstance = validator; // Store for cleanup
        await validator.start();

    } catch (error) {
        if (error.isTtyError) {
            console.error('❌ Interactive mode not supported in this environment');
            console.log('Please use command-line arguments instead. Run with --help for usage.');
        } else {
            console.error('❌ Error:', error.message);
        }
        process.exit(1);
    }
}

// Command-line mode
async function runCommandLineMode(args) {
    const options = parseArgs();

    console.log('Configuration:');
    console.log(`  Input file: ${options.inputFile}`);
    console.log(`  Workers: ${options.maxWorkers}`);
    console.log(`  Good file: ${options.goodFile}`);
    console.log(`  Bad file: ${options.badFile}`);
    console.log(`  Proxy file: ${options.proxyFile}`);
    console.log(`  Use proxy: ${options.useProxy}`);
    console.log('');

    const validator = new GmxValidator(options);
    validatorInstance = validator; // Store for cleanup
    await validator.start();
}

// Global validator instance for cleanup
let validatorInstance = null;

// Handle process termination gracefully
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT. Shutting down gracefully...');
    if (validatorInstance) {
        await validatorInstance.cleanup();
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM. Shutting down gracefully...');
    if (validatorInstance) {
        await validatorInstance.cleanup();
    }
    process.exit(0);
});

// Start the application
if (require.main === module) {
    main();
}

module.exports = { GmxValidator, defaultConfig };
