const Imap = require('imap');

// Simple GMX IMAP test
function testGmxCredential(email, password) {
    return new Promise((resolve) => {
        console.log(`Testing: ${email}`);
        
        const config = {
            user: email,
            password: password,
            host: 'imap.gmx.net',
            port: 143,
            tls: false,
            autotls: 'required',
            tlsOptions: {
                rejectUnauthorized: false
            },
            connTimeout: 10000,
            authTimeout: 8000
        };

        const imap = new Imap(config);
        let resolved = false;

        // Timeout handler
        const timeoutId = setTimeout(() => {
            if (!resolved) {
                resolved = true;
                imap.destroy();
                console.log(`❌ ${email} - Timeout`);
                resolve({ email, valid: false, error: 'Timeout' });
            }
        }, 15000);

        // Success handler
        imap.once('ready', () => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                imap.end();
                console.log(`✅ ${email} - Valid`);
                resolve({ email, valid: true });
            }
        });

        // Error handler
        imap.once('error', (err) => {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                
                const errorMsg = err.message || err.toString();
                console.log(`❌ ${email} - Error: ${errorMsg}`);
                
                // Check if it's an authentication error (invalid credentials)
                const isAuthError = errorMsg.toLowerCase().includes('login') || 
                                  errorMsg.toLowerCase().includes('authentication') ||
                                  errorMsg.toLowerCase().includes('invalid');
                
                resolve({ 
                    email, 
                    valid: false, 
                    error: errorMsg,
                    isAuthError: isAuthError
                });
            }
        });

        // Start connection
        try {
            imap.connect();
        } catch (error) {
            if (!resolved) {
                resolved = true;
                clearTimeout(timeoutId);
                console.log(`❌ ${email} - Connection error: ${error.message}`);
                resolve({ email, valid: false, error: error.message });
            }
        }
    });
}

// Test function
async function runTest() {
    console.log('=== Simple GMX Validator Test ===\n');
    
    // Test credentials - try different ones to see if it's account-specific
    const testCredentials = [
        { email: '<EMAIL>', password: 'wrongpassword' }, // This one worked (got auth error)
        { email: '<EMAIL>', password: 'testpass' },   // Try another invalid one
        { email: '<EMAIL>', password: 'F3hmarnx!1337' } // Test the real one last
    ];
    
    for (const cred of testCredentials) {
        const result = await testGmxCredential(cred.email, cred.password);
        
        if (result.valid) {
            console.log(`✅ VALID: ${result.email}`);
        } else if (result.isAuthError) {
            console.log(`❌ INVALID: ${result.email} - ${result.error}`);
        } else {
            console.log(`⚠️  NETWORK ERROR: ${result.email} - ${result.error}`);
        }
        console.log(''); // Empty line for readability
    }
    
    console.log('Test completed!');
    process.exit(0);
}

// Run the test
runTest().catch(console.error);
