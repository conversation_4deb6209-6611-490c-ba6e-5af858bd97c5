{"name": "germanydazzyproject", "version": "1.0.0", "description": "", "main": "index.js", "bin": {"germanydazzyproject": "./index.js"}, "scripts": {"start": "node index.js", "validate": "node index.js", "validate-no-proxy": "node index.js --no-proxy", "help": "node index.js --help", "test": "echo \"Error: no test specified\" && exit 1", "build": "pkg . --out-path dist", "build-win": "pkg . --targets node18-win-x64 --out-path dist", "build-all": "pkg . --targets node18-win-x64,node18-linux-x64,node18-macos-x64 --out-path dist", "build-customer": "node build.js", "package": "node create-customer-package.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"chalk": "^4.1.2", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "imap": "^0.8.19", "inquirer": "^8.2.6", "nodemailer": "^6.9.8", "smtp-connection": "^4.0.2", "socks-proxy-agent": "^8.0.5", "tunnel": "^0.0.6"}, "devDependencies": {"javascript-obfuscator": "^4.1.1", "pkg": "^5.8.1"}, "pkg": {"scripts": ["index.js", "gmx-validator.js", "interactive-cli.js", "proxy-manager.js", "smtp-worker.js"], "assets": ["emails.txt", "proxy.txt"], "outputPath": "dist"}}